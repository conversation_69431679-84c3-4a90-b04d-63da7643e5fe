/* Hide default cursor except on form elements */
html,
body {
	cursor: none;
}

/* Exception for form elements where native cursor is helpful */
input,
textarea,
select,
[contenteditable='true'],
.use-default-cursor {
	cursor: auto !important;
}

/* Allow specific cursor types to be used when needed */
a,
button,
[role='button'] {
	cursor: none;
}

/* Main tracer */
.mouse-tracer {
	position: fixed;
	width: 24px;
	height: 24px;
	border-radius: 50%;
	pointer-events: none;
	z-index: 9999;
	transform: translate(-50%, -50%);
	background: rgba(255, 0, 255, 0.15);
	box-shadow:
		0 0 15px rgba(255, 0, 255, 0.7),
		0 0 30px rgba(255, 0, 255, 0.4);
	backdrop-filter: blur(2px);
	transition:
		width 0.2s ease,
		height 0.2s ease,
		background 0.3s ease;
	mix-blend-mode: screen;
}

.mouse-tracer::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 6px;
	height: 6px;
	background: rgba(0, 255, 255, 0.8);
	border-radius: 50%;
	box-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
	transition:
		width 0.2s ease,
		height 0.2s ease;
}

/* Hover state */
.mouse-tracer.hover {
	width: 40px;
	height: 40px;
	background: rgba(0, 255, 255, 0.15);
	box-shadow:
		0 0 20px rgba(0, 255, 255, 0.7),
		0 0 40px rgba(0, 255, 255, 0.4);
	mix-blend-mode: screen;
}

.mouse-tracer.hover::before {
	width: 8px;
	height: 8px;
	background: rgba(255, 0, 255, 0.8);
	box-shadow: 0 0 15px rgba(255, 0, 255, 0.8);
}

/* Active (click) state */
.mouse-tracer.active {
	transform: translate(-50%, -50%) scale(0.7);
	transition: transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Trail effect */
.trail-point {
	position: fixed;
	width: 12px;
	height: 12px;
	border-radius: 50%;
	pointer-events: none;
	z-index: 9997;
	background: rgba(255, 0, 255, 0.2);
	box-shadow: 0 0 8px rgba(255, 0, 255, 0.5);
	mix-blend-mode: screen;
}

/* Ripple effects */
.ripple {
	position: fixed;
	border-radius: 50%;
	pointer-events: none;
	z-index: 9998;
	transform: translate(-50%, -50%);
	mix-blend-mode: screen;
}

.ripple-primary {
	border: 2px solid rgba(255, 0, 255, 0.6);
	box-shadow:
		0 0 20px rgba(255, 0, 255, 0.4),
		inset 0 0 10px rgba(0, 255, 255, 0.3);
	background: linear-gradient(135deg, rgba(255, 0, 255, 0.1), rgba(0, 255, 255, 0.05));
}

.ripple-accent {
	border: 2px solid rgba(0, 255, 255, 0.6);
	box-shadow:
		0 0 20px rgba(0, 255, 255, 0.4),
		inset 0 0 10px rgba(255, 0, 255, 0.3);
	background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.05));
}

.ripple-mixed {
	border: 2px solid rgba(191, 0, 255, 0.6);
	box-shadow:
		0 0 20px rgba(191, 0, 255, 0.4),
		inset 0 0 10px rgba(0, 255, 255, 0.3);
	background: linear-gradient(
		135deg,
		rgba(255, 0, 255, 0.1),
		rgba(0, 255, 255, 0.1),
		rgba(191, 0, 255, 0.1)
	);
}

/* Media query to disable on mobile/touch devices */
@media (hover: none) and (pointer: coarse) {
	html,
	body {
		cursor: auto !important;
	}

	.mouse-tracer,
	.trail-point,
	.ripple {
		display: none !important;
	}
}
