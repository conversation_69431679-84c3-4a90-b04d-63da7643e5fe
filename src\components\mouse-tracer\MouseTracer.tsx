import { $, component$, useOnDocument, useSignal, useStore, useVisibleTask$ } from '@qwik.dev/core';
import { isBrowser } from '@qwik.dev/core/build';
import './MouseTracer.css';

// Add a utility function to check if we should disable the custom cursor
const shouldDisableCustomCursor = (element: HTMLElement | null): boolean => {
	if (!element) return false;

	// Check for form elements where native cursor is better
	const formElements = ['INPUT', 'TEXTAREA', 'SELECT'];
	if (formElements.includes(element.tagName)) return true;

	// Check for contenteditable elements
	if (element.getAttribute('contenteditable') === 'true') return true;

	// Check for elements with the class 'use-default-cursor'
	if (element.classList.contains('use-default-cursor')) return true;

	// Check parent elements recursively
	return shouldDisableCustomCursor(element.parentElement);
};

interface MousePosition {
	x: number;
	y: number;
	prevX: number;
	prevY: number;
}

interface TrailPoint {
	id: number;
	x: number;
	y: number;
	opacity: number;
	scale: number;
}

interface Ripple {
	id: number;
	x: number;
	y: number;
	size: number;
	opacity: number;
	color: 'primary' | 'accent' | 'mixed';
}

export default component$(() => {
	const mousePos = useStore<MousePosition>({
		x: 0,
		y: 0,
		prevX: 0,
		prevY: 0,
	});

	const isMouseDown = useSignal(false);
	const trail = useSignal<TrailPoint[]>([]);
	const ripples = useSignal<Ripple[]>([]);
	const idCounter = useSignal(0);
	const isHovering = useSignal(false);

	// Track mouse position
	useOnDocument(
		'mousemove',
		$((event: MouseEvent) => {
			mousePos.prevX = mousePos.x;
			mousePos.prevY = mousePos.y;
			mousePos.x = event.clientX;
			mousePos.y = event.clientY;

			// Get the target element
			const target = event.target as HTMLElement;

			// Check if we should use default cursor
			const useDefaultCursor = shouldDisableCustomCursor(target);

			// Check if mouse is hovering over interactive elements
			isHovering.value =
				!useDefaultCursor &&
				(target.tagName === 'A' ||
					target.tagName === 'BUTTON' ||
					target.closest('a') !== null ||
					target.closest('button') !== null);

			// Add trail point if not using default cursor
			if (
				!useDefaultCursor &&
				(Math.abs(mousePos.x - mousePos.prevX) > 5 || Math.abs(mousePos.y - mousePos.prevY) > 5)
			) {
				const newPoint: TrailPoint = {
					id: idCounter.value++,
					x: mousePos.x,
					y: mousePos.y,
					opacity: 0.7,
					scale: 0.5,
				};

				trail.value = [newPoint, ...trail.value.slice(0, 10)];
			}
		})
	);

	// Handle mouse down/up for dragging effects
	useOnDocument(
		'mousedown',
		$(() => {
			isMouseDown.value = true;
		})
	);

	useOnDocument(
		'mouseup',
		$(() => {
			isMouseDown.value = false;
		})
	);

	// Create ripple effect on click
	useOnDocument(
		'click',
		$((event: MouseEvent) => {
			// Get the target element
			const target = event.target as HTMLElement;

			// Check if we should skip ripple effect (for form elements, etc.)
			if (shouldDisableCustomCursor(target)) {
				// For form elements, we might still want a subtle ripple
				if (
					target.tagName === 'INPUT' ||
					target.tagName === 'TEXTAREA' ||
					target.tagName === 'SELECT'
				) {
					const newRipple: Ripple = {
						id: idCounter.value++,
						x: event.clientX,
						y: event.clientY,
						size: 0,
						opacity: 0.5, // Less opacity for form elements
						color: 'primary',
					};

					ripples.value = [...ripples.value, newRipple];

					// Remove ripple after animation completes
					setTimeout(() => {
						ripples.value = ripples.value.filter((r) => r.id !== newRipple.id);
					}, 800); // Shorter duration for form elements
				}
				return;
			}

			// Determine ripple color based on target element
			let color: 'primary' | 'accent' | 'mixed' = 'primary';

			if (target.tagName === 'A' || target.closest('a') !== null) {
				color = 'accent';
			} else if (target.tagName === 'BUTTON' || target.closest('button') !== null) {
				color = 'mixed';
			}

			const newRipple: Ripple = {
				id: idCounter.value++,
				x: event.clientX,
				y: event.clientY,
				size: 0,
				opacity: 1,
				color,
			};

			ripples.value = [...ripples.value, newRipple];

			// Remove ripple after animation completes
			setTimeout(() => {
				ripples.value = ripples.value.filter((r) => r.id !== newRipple.id);
			}, 1500);
		})
	);

	// Animate trail and ripples
	useVisibleTask$(({ cleanup }) => {
		if (!isBrowser) return;

		let animationFrameId: number;

		const animate = () => {
			// Animate trail points
			trail.value = trail.value
				.map((point) => ({
					...point,
					opacity: point.opacity - 0.03,
					scale: point.scale - 0.02,
				}))
				.filter((point) => point.opacity > 0);

			// Animate ripples
			ripples.value = ripples.value.map((ripple) => ({
				...ripple,
				size: ripple.size + 12,
				opacity: ripple.opacity - 0.015,
			}));

			animationFrameId = requestAnimationFrame(animate);
		};

		animationFrameId = requestAnimationFrame(animate);

		cleanup(() => {
			cancelAnimationFrame(animationFrameId);
		});
	});

	// Check if we should disable the custom cursor
	const useDefaultCursor = useSignal(false);

	// Update the useDefaultCursor signal when mouse moves over elements
	useVisibleTask$(({ track }) => {
		track(() => mousePos.x);
		track(() => mousePos.y);

		if (!isBrowser) return;

		// Get the element under the cursor
		const elementUnderCursor = document.elementFromPoint(mousePos.x, mousePos.y);
		useDefaultCursor.value = shouldDisableCustomCursor(elementUnderCursor as HTMLElement);
	});

	return (
		<>
			{/* Mouse tracer - only show when not using default cursor */}
			{!useDefaultCursor.value && (
				<div
					class={`mouse-tracer ${isHovering.value ? 'hover' : ''} ${isMouseDown.value ? 'active' : ''}`}
					style={{
						left: `${mousePos.x}px`,
						top: `${mousePos.y}px`,
					}}
				/>
			)}

			{/* Trail effect - only show when not using default cursor */}
			{!useDefaultCursor.value &&
				trail.value.map((point) => (
					<div
						key={point.id}
						class="trail-point"
						style={{
							left: `${point.x}px`,
							top: `${point.y}px`,
							opacity: point.opacity,
							transform: `translate(-50%, -50%) scale(${point.scale})`,
						}}
					/>
				))}

			{/* Ripple effects - always show for visual feedback */}
			{ripples.value.map((ripple) => (
				<div
					key={ripple.id}
					class={`ripple ripple-${ripple.color}`}
					style={{
						left: `${ripple.x}px`,
						top: `${ripple.y}px`,
						width: `${ripple.size}px`,
						height: `${ripple.size}px`,
						opacity: ripple.opacity,
					}}
				/>
			))}
		</>
	);
});
