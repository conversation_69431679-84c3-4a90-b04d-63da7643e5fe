import { component$, useContext } from '@qwik.dev/core';
import { APP_STATE } from '~/constants';
import { $localize } from '~/utils/localize';
import GitIcon from '../icons/GitIcon';

export default component$(() => {
	const collections = useContext(APP_STATE).collections.filter(
		(item) => item.parent?.name === '__root_collection__' && !!item.featuredAsset
	);

	const navigation = {
		support: [
			{ name: $localize`Help`, href: '#' },
			{ name: $localize`Track order`, href: '#' },
			{ name: $localize`Shipping`, href: '#' },
			{ name: $localize`Returns`, href: '#' },
		],
		company: [
			{ name: $localize`About`, href: '#' },
			{ name: $localize`Contact us`, href: '#' },
			{ name: $localize`FAQ`, href: '#' },
			{ name: $localize`Services`, href: '#' },
		],
	};

	return (
		<footer class="pt-6 border-t border-primary/40 bg-black">
			<div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
				<div class="xl:grid xl:grid-cols-3 xl:gap-8">
					<div class="grid grid-cols-2 gap-8 xl:col-span-2">
						<div class="md:grid md:grid-cols-2 md:gap-8">
							<div>
								<h3 class="text-sm font-semibold tracking-wider uppercase hero-gradient-text">{$localize`Shop`}</h3>
								<ul class="mt-4 space-y-4">
									{collections.map((collection) => (
										<li key={collection.id}>
											<a
												class="text-base text-text hover:text-accent1 transition-colors duration-300 relative group"
												href={`/collections/${collection.slug}`}
												key={collection.id}
											>
												<span class="relative z-10">{collection.name}</span>
												<span class="absolute inset-0 h-0.5 bg-accent1 bottom-0 w-0 group-hover:w-full transition-all duration-300"></span>
											</a>
										</li>
									))}
								</ul>
							</div>
							<div class="mt-12 md:mt-0">
								<h3 class="text-sm font-semibold tracking-wider uppercase hero-gradient-text">
									{$localize`Support`}
								</h3>
								<ul class="mt-4 space-y-4">
									{navigation.support.map((item) => (
										<li key={item.name}>
											<a
												href={item.href}
												class="text-base text-text hover:text-accent1 transition-colors duration-300 relative group"
											>
												<span class="relative z-10">{item.name}</span>
												<span class="absolute inset-0 h-0.5 bg-accent1 bottom-0 w-0 group-hover:w-full transition-all duration-300"></span>
											</a>
										</li>
									))}
								</ul>
							</div>
						</div>
						<div class="md:grid md:grid-cols-2 md:gap-8">
							<div>
								<h3 class="text-sm font-semibold tracking-wider uppercase hero-gradient-text">
									{$localize`Company`}
								</h3>
								<ul class="mt-4 space-y-4">
									{navigation.company.map((item) => (
										<li key={item.name}>
											<a
												href={item.href}
												class="text-base text-text hover:text-accent1 transition-colors duration-300 relative group"
											>
												<span class="relative z-10">{item.name}</span>
												<span class="absolute inset-0 h-0.5 bg-accent1 bottom-0 w-0 group-hover:w-full transition-all duration-300"></span>
											</a>
										</li>
									))}
								</ul>
							</div>
						</div>
					</div>
					<div class="mt-8 xl:mt-0">
						<h3 class="text-sm font-semibold tracking-wider uppercase hero-gradient-text">
							{$localize`Subscribe to our newsletter`}
						</h3>
						<p class="mt-4 text-base text-text">
							{$localize`Be the first to know about exclusive offers & deals.`}
						</p>
						<div class="mt-4 sm:flex sm:max-w-md">
							<label id="email-subscription" class="sr-only">
								Email address
							</label>
							<input
								type="email"
								autoComplete="email"
								required
								class="input-text"
								placeholder={$localize`Enter your email`}
								aria-labelledby="email-subscription"
							/>
							<div class="mt-3 rounded-md sm:mt-0 sm:ml-3 sm:flex-shrink-0">
								<button
									class="btn-primary glow-primary transform hover:scale-105 transition-transform duration-300 animate-float"
									onClick$={() => {}}
								>
									{$localize`Subscribe`}
								</button>
							</div>
						</div>
					</div>
				</div>
				<div class="mt-8 border-t border-primary/40 pt-8">
					<a
						class="flex items-center space-x-4 font-medium text-text hover:text-accent1 transition-colors duration-300 group"
						target="_blank"
						href="https://github.com/vendure-ecommerce/storefront-qwik-starter"
					>
						<GitIcon class="group-hover:text-accent1 transition-colors duration-300" />
						<span class="relative">
							github.com/vendure-ecommerce/storefront-qwik-starter
							<span class="absolute inset-0 h-0.5 bg-accent1 bottom-0 w-0 group-hover:w-full transition-all duration-300"></span>
						</span>
					</a>
				</div>
			</div>
		</footer>
	);
});
