const colors = require('tailwindcss/colors');

module.exports = {
	content: ['./src/**/*.{js,ts,jsx,tsx}'],
	theme: {
		extend: {
			colors: {
				primary: {
					DEFAULT: '#FF00FF', // Neon Pink
					...colors.pink, // Keep existing shades if needed, or define specific shades
				},
				secondary: {
					DEFAULT: '#BF00FF', // Neon Purple
					...colors.purple,
				},
				accent1: '#00FFFF', // Neon Cyan
				accent2: '#00FF00', // Neon Green
				// Add more accent colors as needed
				background: '#1A1A1A', // Dark background
				text: '#FFFFFF', // White text
				// You might want to define shades for these as well
			},
			animation: {
				'fade-in-up': 'fadeInUp 0.8s ease-out forwards',
				'fade-in': 'fadeIn 0.8s ease-out forwards',
				'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
				'slow-zoom': 'slowZoom 20s ease-in-out infinite alternate',
				float: 'float 3s ease-in-out infinite alternate',
			},
			keyframes: {
				fadeInUp: {
					'0%': { opacity: '0', transform: 'translateY(20px)' },
					'100%': { opacity: '1', transform: 'translateY(0)' },
				},
				fadeIn: {
					'0%': { opacity: '0' },
					'100%': { opacity: '1' },
				},
				slowZoom: {
					'0%': { transform: 'scale(1.0)' },
					'100%': { transform: 'scale(1.1)' },
				},
				float: {
					'0%': { transform: 'translateY(0px)' },
					'100%': { transform: 'translateY(-10px)' },
				},
			},
			transitionDuration: {
				2000: '2000ms',
				3000: '3000ms',
				5000: '5000ms',
				10000: '10000ms',
			},
		},
		variants: {
			extend: {
				opacity: ['disabled'],
				transform: ['hover', 'focus'],
				scale: ['hover', 'focus'],
			},
		},
	},
	plugins: [require('@tailwindcss/forms')],
};
