@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	body {
		@apply bg-background text-text;
	}
}

@layer components {
	.btn-primary {
		@apply w-full border border-transparent rounded-md py-2 px-4 flex items-center justify-center text-base font-medium text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-primary;
	}

	.input-text {
		@apply appearance-none min-w-0 w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-4 text-base text-text placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white focus:border-white focus:placeholder-gray-300;
	}

	.glow-primary {
		box-shadow: 0 0 15px rgba(255, 0, 255, 0.5);
	}

	.glow-accent {
		box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
	}

	.hero-gradient-text {
		background: linear-gradient(to right, #00ffff, #ff00ff, #bf00ff);
		-webkit-background-clip: text;
		background-clip: text;
		color: transparent;
		animation: gradient-shift 8s ease infinite;
		background-size: 200% auto;
	}

	@keyframes gradient-shift {
		0% {
			background-position: 0% 50%;
		}
		50% {
			background-position: 100% 50%;
		}
		100% {
			background-position: 0% 50%;
		}
	}
}
